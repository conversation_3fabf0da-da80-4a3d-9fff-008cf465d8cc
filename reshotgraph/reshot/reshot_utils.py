import os
import numpy as np
import open3d as o3d

def upper_hemisphere_fi<PERSON><PERSON>(samples=64, radius=1.5):
    points = []
    offset = 2. / samples
    increment = np.pi * (3. - np.sqrt(5))
    i = 0
    attempts = 0
    max_attempts = samples * 10
    while len(points) < samples and attempts < max_attempts:
        y = i * offset - 1 + offset / 2
        r = np.sqrt(np.clip(1 - y * y, 0, 1))
        phi = i * increment
        x = np.cos(phi) * r
        z = np.sin(phi) * r
        if y > 0:
            i += 1
            attempts += 1
            continue
        points.append([x * radius, z * radius, y * radius])
        i += 1
        attempts += 1
    return np.array(points)

def visibility_score(pcd, cam_location, radius=100.0):
    try:
        _, pt_map = pcd.hidden_point_removal(cam_location, radius)
        return len(pt_map) / len(pcd.points)
    except Exception as e:
        print(f"[warn] visibility_score failed at {cam_location}: {e}")
        return 0.0

def upright_score(view_dir, gravity=np.array([0, 0, 1])):
    return 1 - abs(np.dot(view_dir, gravity))

def render_with_visualizer(pcd, cam_pos, output_path, width=512, height=512):
    vis = o3d.visualization.Visualizer()
    vis.create_window(visible=False, width=width, height=height)
    vis.add_geometry(pcd)

    ctr = vis.get_view_control()
    center = pcd.get_center()
    up = np.array([0, 0, 1])
    front = (center - cam_pos)
    front /= np.linalg.norm(front)
    ctr.set_lookat(center)
    ctr.set_front(front)
    ctr.set_up(up)
    ctr.set_zoom(0.7)

    vis.poll_events()
    vis.update_renderer()

    img = vis.capture_screen_float_buffer(False)
    vis.destroy_window()

    img_np = (np.asarray(img) * 255).astype(np.uint8)
    o3d.io.write_image(output_path, o3d.geometry.Image(img_np))
    return img_np

def single_pcd_reshot(object, output_dir, samples=64, up_ratio=0.2, top_k=4):
    # bbox = pcd.get_axis_aligned_bounding_box()
    index, pcd, bbox = object["index"], object["pcd"], object["bbox"]
    radius = np.linalg.norm(bbox.get_extent()) * 1.5
    center = pcd.get_center()

    candidate_dirs = upper_hemisphere_fibonacci(samples=samples, radius=radius)
    candidate_positions = candidate_dirs + center

    scores = []
    for cam_pos in candidate_positions:
        view_dir = center - cam_pos
        view_dir /= np.linalg.norm(view_dir)
        vis_score = visibility_score(pcd, cam_pos)
        up_score = upright_score(view_dir)
        final_score = (1 - up_ratio) * vis_score + up_ratio * up_score
        scores.append((final_score, cam_pos))

    scores.sort(key=lambda x: x[0], reverse=True)
    top_views = scores[:top_k]

    # render
    for i, (score, cam_pos) in enumerate(top_views):
        out_path = os.path.join(output_dir, f"{index}_top{i+1}.png")
        object["reshot_path"].append(out_path)
        render_with_visualizer(pcd, cam_pos, out_path)
    
    print(f"Saved {top_k} views for object {index}")