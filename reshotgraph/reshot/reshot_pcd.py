import tqdm
import os
import pickle
import gzip
from typing import Any
from pathlib import Path
from reshotgraph.graph.graph_classes import MapObjectList
from reshotgraph.utils.file_utils import get_config, load_gzip_objects
from reshotgraph.reshot.reshot_utils import single_pcd_reshot


def main(config: dict[str, Any]):
    # load objects from merging results
    run_id = config["run_id"]
    results_config = config["Results"]
    data_config = config["Dataset"]
    result_path = Path(results_config["basedir"]) / data_config["name"] / data_config["sequence"] / "merging" / run_id / "post_results.pkl.gz"
    
    objects = MapObjectList()
    load_gzip_objects(result_path, objects)
    
    # create reshot output directory
    reshot_images_dir = Path(results_config["basedir"]) / data_config["name"] / data_config["sequence"] / "reshot" / run_id / "reshot_images"
    os.makedirs(reshot_images_dir, exist_ok=True)
    
    
    # reshot
    reshot_config = config["Reshot"]
    
    for index, object in tqdm(enumerate(objects), total=len(objects)):
        single_pcd_reshot(
            object=object,
            output_dir=reshot_images_dir,
            sample=reshot_config["samples"],
            up_ratio=reshot_config["up_ratio"],
            top_k=reshot_config["top_k"],
        )
    
    
    results = {
        "objects": objects.to_serializable(),
        "config": config,
    }
    # create reshot object directory
    reshot_objects_dir = Path(results_config["basedir"]) / data_config["name"] / data_config["sequence"] / "reshot" / run_id / "reshot_objects"
    os.makedirs(reshot_objects_dir, exist_ok=True)
    
    # save reshot objects
    print(f"Saving {len(objects)} reshot objects......")
    with gzip.open(Path(reshot_objects_dir) / "reshot_objects.pkl.gz", "wb") as f:
        pickle.dump(results, f)
    
    # continue saving run id
    run_id_dir = reshot_objects_dir.parent
    with open(Path(run_id_dir) / "run_id.txt", "w") as f:
        f.write(run_id)   
    

if __name__ == "__main__":
    config_path = '/home/<USER>/phd_ws/reshotgraph/reshotgraph/reshot/reshot_pcd.yaml'
    config = get_config(path=config_path)
    main(config)