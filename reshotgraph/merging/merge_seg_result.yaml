run_id: 2025-07-27_06_36b7

Dataset:
  name: Replica
  config_path: /home/<USER>/phd_ws/reshotgraph/reshotgraph/dataset/dataset_configs/replica/replica.yaml
  start: 0
  end: -1
  stride: 5
  basedir: /home/<USER>/phd_ws/dataset/Replica
  # scene name, often change
  sequence: room0
  # depth resize
  desired_height: 680
  desired_width: 1200

Detection:
  basedir: /home/<USER>/phd_ws/reshotgraph/reshotgraph/results

Filter:
  mask_min_area_threshold: 25
  mask_min_conf_threshold: 0.2
  bbox_max_area_ratio: 1.0
  pc_min_num_threshold: 22
  # voxel size, the unit is meter
  downsample_voxel_size: 0.01
  use_dbscan: True
  dbscan_eps: 0.05
  dbscan_min_points: 10
  denoise_object_interval: 20
  detection_min_num_thershold: 3
  merge_overlap_threshold: 0.7
  merge_visual_sim_threshold: 0.7

Similarity:
  spatial_sim_type: neareast
  aggregate_ratio: 0.5
  final_sim_threshold: 0.6

Device: cuda

Result:
  basedir: /home/<USER>/phd_ws/reshotgraph/reshotgraph/results