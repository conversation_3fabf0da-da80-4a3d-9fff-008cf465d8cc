import torch
import numpy as np
from typing import Any
from tqdm import trange
from pathlib import Path
import pickle
import gzip
import os 

from reshotgraph.utils.general_utils import to_tensor
from reshotgraph.utils.file_utils import get_config, read_rgb_pil_image
from reshotgraph.dataset.dataset_common import get_dataset
from reshotgraph.graph.graph_classes import MapObjectList, DetectionList
from reshotgraph.merging.merge_utils import(
    resize_gobs, 
    filter_gobs,
    mask_subtract_contained,
    create_object_pcd,
    compute_spatial_similarities,
    compute_visual_similarities,
    aggregate_similarities,
    merge_detections_to_objects,
    denoise_objects,
    filter_objects,
    merge_objects,
)
from reshotgraph.utils.pointcloud_utils import get_bounding_box, downsample_pcd

def get_specific_dataset(config: dict[str, Any]) -> Any:
    dataset = get_dataset(
        dataconfig = config["config_path"],
        start = config["start"],
        end = config["end"],
        stride = config["stride"],
        basedir = config["basedir"],
        sequence = config["sequence"],
        desired_height = config["desired_height"],
        desired_width = config["desired_width"],
        device="cpu",
        dtype=torch.float,
    )
    print(f"Loaded dataset done with {len(dataset)} frames......")
    return dataset

def gobs_to_detection_list(
    cfg, 
    image, 
    depth_array,
    cam_K, 
    idx, 
    gobs, 
    trans_pose = None,
    color_path = None,
):
    detection_list = DetectionList()
    gobs = resize_gobs(gobs, image)
    gobs = filter_gobs(cfg, gobs, image)
    
    if len(gobs['xyxy']) == 0:
        return detection_list
    
    xyxy = gobs['xyxy']
    mask = gobs['mask']
    gobs['mask'] = mask_subtract_contained(xyxy, mask)
    
    n_masks = len(gobs['xyxy'])
    for mask_idx in range(n_masks):
        mask = gobs['mask'][mask_idx]
        camera_object_pcd = create_object_pcd(
            depth_array,
            mask,
            cam_K,
            image,
            obj_color = None # None is using rgb color
        )
        
        # at least contains N points
        if len(camera_object_pcd.points) < max(cfg["pc_min_num_threshold"], 5): 
            continue
        
        # make transform from camera to global
        if trans_pose is not None:
            global_object_pcd = camera_object_pcd.transform(trans_pose)
        else:
            global_object_pcd = camera_object_pcd
        
        # get largest cluster, filter out noise 
        global_object_pcd = downsample_pcd(
            global_object_pcd, 
            downsample_voxel_size=cfg["downsample_voxel_size"],
            use_dbscan=cfg["use_dbscan"],
            dbscan_eps=cfg["dbscan_eps"],
            dbscan_min_points=cfg["dbscan_min_points"],
        )
        
        pcd_bbox = get_bounding_box(global_object_pcd, use_oriented_bbox=True)
        pcd_bbox.color = [0,1,0]
        if pcd_bbox.volume() < 1e-6:
            continue
        
        detected_object = {
            'image_idx' : [idx],                             # idx of the image
            'mask_idx' : [mask_idx],                         # idx of the mask/detection
            'color_path' : [color_path],                     # path to the RGB image
            'num_detections' : 1,                            # number of detections in this object
            'mask': [mask],
            'xyxy': [gobs['xyxy'][mask_idx]],
            'conf': [gobs['confidence'][mask_idx]],
            'n_points': [len(global_object_pcd.points)],
            'pixel_area': [mask.sum()],
            "inst_color": np.random.rand(3),                 # A random color used for this segment instance
            "reshot_path": [None],                           # path to the reshot image, will be generated later
            # These are for the entire 3D object
            'pcd': global_object_pcd,
            'bbox': pcd_bbox,
            'clip_ft': to_tensor(gobs['image_feats'][mask_idx]),
            'text_ft': to_tensor(gobs['text_feats'][mask_idx]),
        }
        
        detection_list.append(detected_object)
        
    return detection_list
    


def main(config: dict[str, Any]):
    # load result run_id
    run_id = config["run_id"]
    
    # load Replica dataset for depth
    data_config = config["Dataset"]
    dataset = get_specific_dataset(data_config)
    
    # initialize objects list
    objects = MapObjectList(device=config["Device"])
    
    for idx in trange(len(dataset)):
        color_tensor, depth_tensor, intrinsics, *_ = dataset[idx]
        
        color_path = dataset.color_paths[idx]
        color_path = Path(color_path)
        image_rgb, image_pil = read_rgb_pil_image(color_path)
        
        color_np = color_tensor.cpu().numpy() # (H, W, 3)
        image_rgb_1 = (color_np).astype(np.uint8) # (H, W, 3)
        assert image_rgb_1.max() > 1, "Image is not in range [0, 255]"
        
        # get depth
        depth_tensor = depth_tensor[..., 0]
        depth_array = depth_tensor.cpu().numpy()
        
        # Get the intrinsics matrix
        cam_K = intrinsics.cpu().numpy()[:3, :3]
        
        # Get detections
        detections_config = config["Detection"]
        detections_basedir = Path(detections_config["basedir"]) / data_config["name"] / data_config["sequence"] / "segmentation" / run_id / "detections"
        detections_path = detections_basedir / color_path.name
        detections_path = detections_path.with_suffix(".pkl.gz")
        
        gobs = None
        with gzip.open(detections_path, "rb") as f:
            gobs = pickle.load(f)
            
        # get original pose before transform
        untrans_pose = dataset.poses[idx]
        untrans_pose = untrans_pose.cpu().numpy()
        
        # convert gobs to detection list
        detection_list = gobs_to_detection_list(
            cfg=config["Filter"],
            image=image_rgb, 
            depth_array=depth_array,
            cam_K=cam_K, 
            idx=idx, 
            gobs=gobs, 
            trans_pose=untrans_pose,
            color_path=color_path,
        )
        
        if len(detection_list) == 0:
            continue
        
        
        # for the first frame, add all detections to the map
        if len(objects) == 0:
            for i in range(len(detection_list)):
                objects.append(detection_list[i])
            # and skip the similarity computation 
            continue
        
        # compure similarity
        sim_config = config["Similarity"]
        spatial_sim = compute_spatial_similarities(
            sim_type=sim_config["spatial_sim_type"],
            detection_list=detection_list, 
            objects=objects, 
            nearest_distance=config["Filter"]["downsample_voxel_size"],    
        )
        visual_sim = compute_visual_similarities(detection_list, objects)
        aggregate_sim = aggregate_similarities(sim_config["aggregate_ratio"], spatial_sim, visual_sim)
        
        aggregate_sim[aggregate_sim < sim_config["final_sim_threshold"]] = float('-inf')
        
        # merge detections to objects
        objects = merge_detections_to_objects(config, detection_list, objects, aggregate_sim)
        
        # denoise objects
        denoise_interval =  config["Filter"]["denoise_object_interval"]
        if denoise_interval > 0 and idx % denoise_interval == 0:
            objects = denoise_objects(config["Filter"], objects)
            
        # # for debug
        # if idx == 10:
        #     break
            
    # denoise again
    objects = denoise_objects(config["Filter"], objects)
    
    # save results before post processing
    results = {
        "objects": objects.to_serializable(),
        "config": config,
    }
    result_basedir = Path(config["Result"]["basedir"]) / data_config["name"] / data_config["sequence"] / "merging" / run_id
    os.makedirs(result_basedir, exist_ok=True)
    with open(Path(result_basedir) / "run_id.txt", "w") as f:
        f.write(run_id)   # continue saving run id
    
    origin_result_save_path = result_basedir / "origin_results.pkl.gz"
    print(f"Saving {len(objects)} origin results......")
    with gzip.open(origin_result_save_path, "wb") as f:
        pickle.dump(results, f)

    # post processing
    objects = filter_objects(config["Filter"], objects)
    objects = merge_objects(config, objects)
    
    # save after posr processing
    results["objects"] = objects.to_serializable()
    post_result_save_path = result_basedir / "post_results.pkl.gz"
    print(f"Saving {len(objects)} post results......")
    with gzip.open(post_result_save_path, "wb") as f:
        pickle.dump(results, f)
        
    
if __name__ == "__main__":
    config_path = '/home/<USER>/phd_ws/reshotgraph/reshotgraph/merging/merge_seg_result.yaml'
    config = get_config(path=config_path)
    main(config)