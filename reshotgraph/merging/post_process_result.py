from reshotgraph.utils.file_utils import get_config, load_gzip_objects
from typing import Any
import pickle
import gzip
import numpy as np
from pathlib import Path
from reshotgraph.graph.graph_classes import MapObjectList


def main(config: dict[str, Any]):
    run_id = config["run_id"]
    detections_config = config["Detection"]
    data_config = config["Dataset"]
    origin_result_path = Path(detections_config["basedir"]) / data_config["name"] / data_config["sequence"] / "merging" / run_id / "origin_results.pkl.gz"
    
    objects = MapObjectList()
    load_gzip_objects(origin_result_path, objects)
    print(len(objects))
    print("load origin result done......")
    pass




if __name__ == "__main__":
    config_path = '/home/<USER>/phd_ws/reshotgraph/reshotgraph/merging/merge_seg_result.yaml'
    config = get_config(path=config_path)
    main(config)