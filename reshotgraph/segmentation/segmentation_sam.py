import os
import yaml
import argparse
import torch
import cv2
import gzip
import pickle
import warnings
import numpy as np
import supervision as sv
from PIL import Image
from tqdm import trange
from typing import Any
from pathlib import Path


from reshotgraph.utils.general_utils import generate_run_id
from reshotgraph.utils.model_utils import compute_clip_features
from reshotgraph.utils.visual_utils import vis_result_fast
from reshotgraph.utils.file_utils import get_config, read_rgb_pil_image

### import SAM ###
try: 
    from segment_anything import   \
        sam_model_registry,        \
        SamPredictor,              \
        SamAutomaticMaskGenerator 
except ImportError as e:
    print("Import Error: Please install Grounded Segment Anything first")
    raise e

### import CLIP ###
import open_clip

### import dataset ###
from reshotgraph.dataset.dataset_common import get_dataset

def get_sam_mask_generator(sam_config: dict[str, Any], device: str | int) -> SamAutomaticMaskGenerator:
    if sam_config["name"] == "SAM":
        sam = sam_model_registry[sam_config["encoder_version"]](checkpoint=sam_config["checkpoint"])
        sam.to(device)
        mask_generator = SamAutomaticMaskGenerator(
            model = sam,
            points_per_side = sam_config["points_per_side"],
            points_per_batch = sam_config["points_per_batch"],
            pred_iou_thresh = sam_config["pred_iou_thresh"],
            stability_score_thresh = sam_config["stability_score_thresh"],
            crop_n_layers = sam_config["crop_n_layers"],
            min_mask_region_area = sam_config["min_mask_region_area"],
        )
        return mask_generator
    else:
        print("Model name in yaml is not correct!")
        raise NotImplementedError
    
def get_clip_model(clip_config: dict[str, Any], device: str | int) -> tuple[Any, Any, Any]:
    clip_model, _, clip_preprocess = open_clip.create_model_and_transforms(
        model_name = clip_config["encoder_version"], 
        pretrained = clip_config["checkpoint"]
    )
    clip_tokenizer = open_clip.get_tokenizer(clip_config["encoder_version"])
    clip_model = clip_model.to(device)
    return clip_model, clip_tokenizer, clip_preprocess

def get_specific_dataset(config: dict[str, Any]) -> Any:
    dataset = get_dataset(
        dataconfig = config["config_path"],
        start = config["start"],
        end = config["end"],
        stride = config["stride"],
        basedir = config["basedir"],
        sequence = config["sequence"],
        desired_height = config["desired_height"],
        desired_width = config["desired_width"],
        device="cpu",
        dtype=torch.float,
    )
    print(f"Loaded dataset done with {len(dataset)} frames......")
    return dataset

def get_sam_segmentation_dense(
    model_name: str, 
    generator: Any,
    image: np.ndarray
) -> tuple[np.ndarray, np.ndarray, np.ndarray]:
    '''
    The SAM based on automatic mask generation, without bbox prompting
    Args:
        image: (H, W, 3), in RGB color space, in range [0, 255]
    Returns:
        mask: (N, H, W)
        xyxy: (N, 4)
        conf: (N,)
    '''
    if model_name == "SAM":
        results = generator.generate(image)
        mask, xyxy, conf = [], [], []
        for r in results:
            # append mask
            mask.append(r["segmentation"])
            # convert xywh to xyxy
            xyhw = r["bbox"].copy()
            xyhw[2] += xyhw[0]
            xyhw[3] += xyhw[1]
            xyxy.append(xyhw)
            # append confidence
            conf.append(r["predicted_iou"])
        # apply np.array to each list 
        mask, xyxy, conf = map(np.array, (mask, xyxy, conf))
        return mask, xyxy, conf
    else:
        raise NotImplementedError

def sam_results_to_detections(xyxy, conf, mask):
    return sv.Detections(
        xyxy=xyxy,
        confidence=conf,
        class_id=np.zeros_like(conf, dtype=int),
        mask=mask,
    )



def main(config: dict[str, Any]):
    device = config["Device"]
    
    # Initialize SAM
    sam_config = config["SAM"]
    mask_generator = get_sam_mask_generator(sam_config, device)
    
    # Initialize CLIP
    clip_config = config["CLIP"]
    clip_model, clip_tokenizer, clip_preprocess = get_clip_model(clip_config, device)
    
    # Initialize the dataset
    data_config = config["Dataset"]
    dataset = get_specific_dataset(data_config)
    
    # specify unic id
    run_id = generate_run_id()
    result_config = config["Result"]
    result_basedir = Path(result_config["basedir"]) / data_config["name"] / data_config["sequence"] / "segmentation" / run_id
    os.makedirs(result_basedir, exist_ok=True)
    with open(Path(result_basedir) / "run_id.txt", "w") as f:
        f.write(run_id)
    
    classes = ['item']
    for idx in trange(len(dataset)): # same as tqdm(range(len(dataset)))
        color_path = dataset.color_paths[idx]
        
        image_rgb, image_pil = read_rgb_pil_image(color_path)
        
        mask, xyxy, conf = get_sam_segmentation_dense(sam_config["name"], mask_generator, image_rgb)
        
        detections = sam_results_to_detections(xyxy, conf, mask)
        
        image_crops, image_feats, text_feats = compute_clip_features(
            clip_model=clip_model,
            clip_preprocess=clip_preprocess,
            clip_tokenizer=clip_tokenizer,
            device=device,
            image=image_pil,
            detections=detections,
            classes=classes
        )
        
        # visualize the results: image with masks(annotated)
        annotated_image, labels = vis_result_fast(image_rgb, detections, classes, instance_random_color=True)
        
        # save annotated image
        color_path = Path(color_path)
        annotated_image_path = result_basedir / "annotated_image" / color_path.name
        os.makedirs(os.path.dirname(annotated_image_path), exist_ok=True)
        cv2.imwrite(annotated_image_path, annotated_image)
        
        # save mask and clip results
        results = {
            "xyxy": detections.xyxy,
            "confidence": detections.confidence,
            "class_id": detections.class_id,
            "mask": detections.mask,
            "classes": classes,
            "image_crops": image_crops,
            "image_feats": image_feats,
            "text_feats": text_feats,
        }
        
        detections_save_path = result_basedir / "detections" / color_path.name.replace(".jpg", ".pkl.gz")
        os.makedirs(os.path.dirname(detections_save_path), exist_ok=True)
        with gzip.open(detections_save_path, "wb") as f:
            pickle.dump(results, f)
        
if __name__ == "__main__":
    config_path = '/home/<USER>/phd_ws/reshotgraph/reshotgraph/segmentation/segmentation_sam.yaml'
    config = get_config(path=config_path)
    main(config)