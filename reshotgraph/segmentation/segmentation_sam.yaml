SAM:
  name: SAM
  encoder_version: vit_h
  checkpoint: /home/<USER>/phd_ws/Grounded-Segment-Anything/sam_vit_h_4b8939.pth
  points_per_side: 12
  points_per_batch: 144
  pred_iou_thresh: 0.88
  stability_score_thresh: 0.95
  crop_n_layers: 0
  min_mask_region_area: 100

CLIP:
  encoder_version: ViT-H-14
  checkpoint: laion2b_s32b_b79k

Dataset:
  name: Replica
  config_path: /home/<USER>/phd_ws/reshotgraph/reshotgraph/dataset/dataset_configs/replica/replica.yaml
  start: 0
  end: -1
  stride: 5
  basedir: /home/<USER>/phd_ws/dataset/Replica
  # scene name, often change
  sequence: room0
  # depth resize
  desired_height: 680
  desired_width: 1200


Device: cuda

Result:
  basedir: /home/<USER>/phd_ws/reshotgraph/reshotgraph/results

  