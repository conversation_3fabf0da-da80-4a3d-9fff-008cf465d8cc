from PIL import Image
import numpy as np
import torch

from reshotgraph.utils.cv_utils import to_PIL_Image

def compute_clip_features(
    clip_model, clip_preprocess, clip_tokenizer, device, 
    image=None, text=None, detections=None, classes=None
):
    """
    change mode according to the last 4 args
    """
    with torch.no_grad():
        if all(x is not None for x in [image, detections, classes]):
            if text is not None:
                raise NotImplementedError

            image = to_PIL_Image(image)
            image_crops, image_feats, text_feats = [], [], []
            padding = 20
        
            for index in range(len(detections.xyxy)):
                x_min, y_min, x_max, y_max = detections.xyxy[index]
                # checking to avoid going beyond the borders
                image_width, image_height = image.size
                pad_left = min(padding, x_min)
                pad_right = min(padding, y_min)
                pad_top = min(padding, image_width - x_max)
                pad_bottom = min(padding, image_height - y_max)
                # do the padding
                x_min -= pad_left
                y_min -= pad_top
                x_max += pad_right
                y_max += pad_bottom
                # crop the image
                cropped_image = image.crop((x_min, y_min, x_max, y_max))
            
                preprocessed_image = clip_preprocess(cropped_image).unsqueeze(0).to(device)
                crop_feat = clip_model.encode_image(preprocessed_image)
                crop_feat /= crop_feat.norm(dim=-1, keepdim=True)
            
                class_id = detections.class_id[index]
                tokenized_text = clip_tokenizer([classes[class_id]]).to(device)
                text_feat = clip_model.encode_text(tokenized_text)
                text_feat /= text_feat.norm(dim=-1, keepdim=True)
            
                crop_feat = crop_feat.cpu().numpy()
                text_feat = text_feat.cpu().numpy()
            
                image_crops.append(cropped_image)
                image_feats.append(crop_feat)
                text_feats.append(text_feat)
        
            # turn the list of feats into np matrices
            image_feats = np.concatenate(image_feats, axis=0)
            text_feats = np.concatenate(text_feats, axis=0)
        
            return image_crops, image_feats, text_feats
        else:
            raise NotImplementedError
            
        
        